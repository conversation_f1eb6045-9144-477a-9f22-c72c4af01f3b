from datetime import datetime

import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IntParameter, IStrategy
from pandas import DataFrame


class BollingerBandBreakoutStrategy(IStrategy):
    """
    布林带变化率交易策略

    基于布林带轨道变化率进行双向交易的策略。

    策略核心逻辑：
    1. 做多条件：布林带上轨和中轨同时向上变化，变化率超过阈值
    2. 做空条件：布林带下轨和中轨同时向下变化，变化率超过阈值
    3. 退出条件：价格回归到布林带中轨附近

    技术指标：
    - 布林带：20周期，2倍标准差的布林带
    - 变化率计算：(当前值 - 前一周期值) / 当前值
    - 上轨变化率：bbu_change
    - 中轨变化率：bbm_change
    - 下轨变化率：bbl_change
    - 价格位置分析：
      * high_position_in_window：最高价在N根K线窗口中的位置（0=当前，1=前1根，以此类推）
      * low_position_in_window：最低价在N根K线窗口中的位置
      * high_in_early_candles：最高价是否在前面几根K线（用于识别顶部回调）
      * low_in_early_candles：最低价是否在前面几根K线（用于识别底部反弹）

    入场逻辑：
    1. 布林带SMA策略：
       - 做多：当布林带上轨和中轨都向上变化且变化率大于阈值时入场
       - 做空：当布林带下轨和中轨都向下变化且变化率小于阈值时入场
    2. 价格突破策略（独立条件）：
       - 做多：当收盘价高于N小时最高价一定比例时入场
       - 做空：当收盘价低于N小时最低价一定比例时入场
    3. 价格位置分析过滤：
       - 最高价位置分析：检测N根K线内最高价出现在前几根K线的位置
       - 最低价位置分析：检测N根K线内最低价出现在前几根K线的位置
       - 用于识别顶部后回调和底部后反弹的交易机会
    4. 通用过滤条件：
       - 价格变化范围过滤：只在N小时内价格变化范围不大的时段开单，避免高波动期间交易
       - 所有阈值通过超参数优化确定最佳值

    风险控制：
    - 支持双向交易（做多和做空）
    - 15%固定止损保护
    - 基于布林带中轨的退出机制
    - 反转信号亏损退出：当出现反转信号且亏损达到阈值时退出
    - 极端盈利/亏损保护
    - 时间止损机制

    适用场景：
    - 波动率突然增加的市场
    - 布林带快速扩张或收缩时的交易机会
    - 短期动量交易

    作者: FreqTrade用户
    时间框架: 1分钟
    """

    # 基础设置
    timeframe = "3m"  # 主时间框架
    can_short = True  # 启用做空交易

    # Buy hyperspace params:
    buy_params = {
        "buy_leverage": 10.0,
        #
        "buy_price_range_num": 40,
        "buy_price_range_threshold": 0.080,
        #
        "buy_bb_width_sma_window": 6,
        "buy_bb_width_expansion_threshold": 0.01,
        #
        "buy_bb_sma_window": 3,
        "buy_bbl_sma_threshold": -0.007,
        "buy_bbu_sma_threshold": 0.001,
        #
        "buy_breakout_long_threshold": 0.005,
        "buy_breakout_short_threshold": 0.005,
        #
        "buy_price_window": 10,
        "buy_high_early_min_pos": 3,
        "buy_high_early_max_pos": 7,
        "buy_low_early_min_pos": 3,
        "buy_low_early_max_pos": 7,
    }

    # Sell hyperspace params:
    sell_params = {
        "sell_bbm_loss_profit_threshold": -0.183,
        "sell_bbm_take_profit_threshold": 0.275,
        #
        "sell_reversal_loss_threshold": -0.084,
        "sell_reversal_profit_threshold": 0.27,
        #
        "sell_long_fastx": 90,
        "sell_short_fastx": 90,
    }

    # Stoploss:
    stoploss = -0.3

    # Trailing stop:
    trailing_stop = False

    # Max Open Trades:
    max_open_trades = 10  # value loaded from strategy

    # =============================================================================
    # 杠杆设置
    # =============================================================================
    is_hyperopt_leverage = True
    buy_leverage = DecimalParameter(1.0, 15.0, default=10.0, space="buy", optimize=is_hyperopt_leverage)  # 杠杆倍数（1-15倍）

    # =============================================================================
    # 价格变化范围过滤参数
    # =============================================================================
    buy_price_range_num = IntParameter(10, 100, default=15, space="buy", optimize=True)  # N根K线时间窗口
    buy_price_range_threshold = DecimalParameter(0.0001, 0.1, default=0.005, space="buy", optimize=True)  # 价格变化范围阈值（5%默认）

    # =============================================================================
    # 布林带均线窗口期参数
    # =============================================================================
    is_bbsma_enable = True
    buy_bb_sma_window = IntParameter(2, 8, default=3, space="buy", optimize=is_bbsma_enable)  # 布林带均线窗口期（2-10周期）
    buy_bbu_sma_threshold = DecimalParameter(0.0001, 0.01, default=0.003, space="buy", optimize=is_bbsma_enable)
    buy_bbl_sma_threshold = DecimalParameter(-0.01, -0.0001, default=-0.004, space="buy", optimize=is_bbsma_enable)
    # 布林带宽度窗口期参数
    buy_bb_width_sma_window = IntParameter(3, 8, default=4, space="buy", optimize=is_bbsma_enable)  # 布林带宽度SMA窗口期
    buy_bb_width_expansion_threshold = DecimalParameter(0.0001, 0.01, default=0.003, space="buy", optimize=is_bbsma_enable)  # 布林带宽度扩张阈值

    # =============================================================================
    # 价格突破条件参数
    # =============================================================================
    is_breakout_enable = True
    buy_breakout_long_threshold = DecimalParameter(0.001, 0.05, default=0.01, space="buy", optimize=is_bbsma_enable)  # 做多突破阈值（1%默认）
    buy_breakout_short_threshold = DecimalParameter(0.001, 0.05, default=0.01, space="buy", optimize=is_bbsma_enable)  # 做空突破阈值（1%默认）

    # =============================================================================
    # 退出参数
    # =============================================================================
    is_fastk_enable = True
    sell_long_fastx = IntParameter(50, 100, default=75, space="sell", optimize=is_fastk_enable)
    sell_short_fastx = IntParameter(50, 100, default=75, space="sell", optimize=is_fastk_enable)

    sell_bbm_loss_profit_threshold = DecimalParameter(-0.2, -0.0001, default=-0.059, space="sell", optimize=True)  # 布林带中轨亏损保护阈值
    sell_bbm_take_profit_threshold = DecimalParameter(0.1, 0.5, default=0.308, space="sell", optimize=True)  # 布林带中轨盈利保护阈值

    # 反转信号退出参数
    sell_reversal_loss_threshold = DecimalParameter(-0.15, -0.001, default=-0.02, space="sell", optimize=True)  # 反转信号亏损退出阈值
    sell_reversal_profit_threshold = DecimalParameter(0.01, 0.3, default=0.05, space="sell", optimize=True)  # 反转信号止盈阈值

    # =============================================================================
    # 保护机制参数
    # =============================================================================
    is_protection_cooldown = False
    cooldown_lookback = IntParameter(2, 10, default=5, space="protection", optimize=is_protection_cooldown)  # 冷却期周期数（2-10根K线）

    @property
    def protections(self):
        """
        配置保护机制

        CooldownPeriod保护：
        - 防止交易对在退出交易后立即重新入场
        - 给其他交易对提供交易机会
        - 避免在同一交易对上过度频繁交易

        Returns:
            list: 保护机制配置列表
        """
        results = []
        if self.is_protection_cooldown:
            results.append({"method": "CooldownPeriod", "stop_duration_candles": self.cooldown_lookback.value})
        return results

    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        # 计算开盘价和收盘价的变化
        df["price_change"] = df["close"] - df["open"]  # 开盘到收盘的价格变化（正值=阳线，负值=阴线）
        df["price_change_ratio"] = df["price_change"] / df["open"]  # 价格变化相对比率
        df["price_change_ratio_10"] = df["price_change_ratio"].rolling(window=10).sum()  # 10周期累计价格变化比率

        # 计算布林带（20周期，2倍标准差）
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(df), window=20, stds=2)
        df["bb_lowerband"] = bollinger["lower"]
        df["bb_middleband"] = bollinger["mid"]
        df["bb_upperband"] = bollinger["upper"]
        df["bb_width"] = (df["bb_upperband"] - df["bb_lowerband"]) / df["bb_middleband"]

        # 计算布林带各轨道的变化率（当前值相对于前一周期的变化百分比）
        df["bb_lower_change_rate"] = (df["bb_lowerband"] - df["bb_lowerband"].shift(1)) / df["bb_lowerband"]  # 下轨变化率
        df["bb_middle_change_rate"] = (df["bb_middleband"] - df["bb_middleband"].shift(1)) / df["bb_middleband"]  # 中轨变化率
        df["bb_upper_change_rate"] = (df["bb_upperband"] - df["bb_upperband"].shift(1)) / df["bb_upperband"]  # 上轨变化率

        # 布林带轨道突破条件
        # 上轨突破：价格突破上轨 + 下轨向下变化（布林带扩张）
        df["bb_upper_breakout"] = (df["close"] > df["bb_upperband"]) & (df["bb_lower_change_rate"] < 0)
        # 下轨突破：价格突破下轨 + 上轨向上变化（布林带扩张）
        df["bb_lower_breakout"] = (df["close"] < df["bb_lowerband"]) & (df["bb_upper_change_rate"] > 0)

        # 布林带变化率错误信号（价格与布林带变化方向不一致）
        df["bb_lower_error"] = (df["bb_lower_change_rate"] < 0) & (df["bb_middle_change_rate"] < 0) & (df["price_change"] > 0)  # 下轨下降但价格上涨
        df["bb_upper_error"] = (df["bb_upper_change_rate"] > 0) & (df["bb_middle_change_rate"] > 0) & (df["price_change"] < 0)  # 上轨上升但价格下跌

        # 布林带轨道移动平均线
        df["bb_lower_sma"] = df["bb_lowerband"].rolling(window=self.buy_bb_sma_window.value, min_periods=1).mean()  # 下轨SMA
        df["bb_middle_sma"] = df["bb_middleband"].rolling(window=self.buy_bb_sma_window.value, min_periods=1).mean()  # 中轨SMA
        df["bb_upper_sma"] = df["bb_upperband"].rolling(window=self.buy_bb_sma_window.value, min_periods=1).mean()  # 上轨SMA
        df["bb_width_sma"] = df["bb_width"].rolling(window=self.buy_bb_width_sma_window.value, min_periods=1).mean()  # 宽度SMA

        # 布林带轨道相对于其SMA的比率（用于判断轨道变化趋势）
        df["bb_lower_sma_ratio"] = (df["bb_lowerband"] - df["bb_lower_sma"]) / df["bb_lowerband"]  # 下轨相对SMA比率
        df["bb_middle_sma_ratio"] = (df["bb_middleband"] - df["bb_middle_sma"]) / df["bb_middleband"]  # 中轨相对SMA比率
        df["bb_upper_sma_ratio"] = (df["bb_upperband"] - df["bb_upper_sma"]) / df["bb_upperband"]  # 上轨相对SMA比率
        df["bb_width_expansion_ratio"] = (df["bb_width"] - df["bb_width_sma"].shift(1)) / df["bb_width"]  # 宽度扩张比率

        # Cofi
        stoch_fast = ta.STOCHF(df, 5, 3, 0, 3, 0)
        df["fastd"] = stoch_fast["fastd"]
        df["fastk"] = stoch_fast["fastk"]
        df["adx"] = ta.ADX(df)

        # =============================================================================
        # 计算价格变化范围过滤器
        # =============================================================================

        df["price_range_min"] = df["low"].rolling(window=self.buy_price_range_num.value, min_periods=1).min()
        df["price_range_max"] = df["high"].rolling(window=self.buy_price_range_num.value, min_periods=1).max()
        df["price_range_middle"] = (df["price_range_min"] + df["price_range_max"]) / 2
        df["price_range_width"] = (df["price_range_max"] - df["price_range_min"]) / df["price_range_middle"]
        df["price_range_width_change"] = (df["price_range_width"] - df["price_range_width"].shift(1)) / df["price_range_width"]

        # =============================================================================
        # EMA指标
        # =============================================================================
        df["ema_200"] = df["close"].ewm(span=200, adjust=False).mean()  # 200周期EMA慢线
        df["bbu_ema200_gold_cross"] = qtpylib.crossed_above(df["bb_upperband"], df["ema_200"])
        df["bbl_ema200_dead_cross"] = qtpylib.crossed_below(df["bb_lowerband"], df["ema_200"])
        # EMA快慢线交叉指标
        df["ema_12"] = df["close"].ewm(span=12, adjust=False).mean()  # 12周期EMA快线
        df["ema_26"] = df["close"].ewm(span=26, adjust=False).mean()  # 26周期EMA慢线
        df["ema_50"] = df["close"].ewm(span=50, adjust=False).mean()  # 50周期EMA中线
        # EMA交叉信号计算
        df["ema_golden_cross"] = qtpylib.crossed_above(df["ema_12"], df["ema_26"])  # 金叉：快线上穿慢线（看涨信号）
        df["ema_death_cross"] = qtpylib.crossed_below(df["ema_12"], df["ema_26"])  # 死叉：快线下穿慢线（看跌信号）
        # EMA多空排列判断
        df["ema_bullish_alignment"] = (df["ema_12"] > df["ema_26"]) & (df["ema_26"] > df["ema_50"])  # 多头排列：快线>慢线>中线
        df["ema_bearish_alignment"] = (df["ema_12"] < df["ema_26"]) & (df["ema_26"] < df["ema_50"])  # 空头排列：快线<慢线<中线
        # EMA与价格关系
        df["price_above_ema12"] = df["close"] > df["ema_12"]  # 价格在快线上方
        df["price_below_ema12"] = df["close"] < df["ema_12"]  # 价格在快线下方
        df["price_above_ema26"] = df["close"] > df["ema_26"]  # 价格在慢线上方
        df["price_below_ema26"] = df["close"] < df["ema_26"]  # 价格在慢线下方

        # =============================================================================
        # 辅助指标
        # =============================================================================
        df["zero"] = 0  # 零线，用于图表显示
        return df

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        生成入场信号

        1. 布林带SMA策略：
           - 做多：当布林带上轨和中轨都向上变化且变化率大于阈值时入场
           - 做空：当布林带下轨和中轨都向下变化且变化率小于阈值时入场
        2. 价格突破策略（独立条件）：
           - 做多：当收盘价高于N小时最高价一定比例时入场

        Args:
            df: 包含所有技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """

        # 计算基础条件
        base_condition = (
            True  # 始终为真的占位符
            & (df["volume"] > 0)  # 确保有交易量，避免在无交易时段入场
            & (df["price_range_width_change"] > self.buy_price_range_threshold.value)  # 价格变化范围过滤
            & (df["price_range_width_change"].shift(1) < self.buy_price_range_threshold.value)  # 价格变化范围过滤
        )

        # =============================================================================
        # 计算布林带SMA策略条件
        # =============================================================================
        if self.is_bbsma_enable:
            # 布林带SMA做多条件：中轨和上轨都向上变化
            bb_sma_long_condition = (
                base_condition  # 基本条件
                & (df["bb_middle_sma_ratio"] > self.buy_bbu_sma_threshold.value)  # 中轨相对SMA上升
                & (df["bb_upper_sma_ratio"] > self.buy_bbu_sma_threshold.value)  # 上轨相对SMA上升
                & (df["price_change"] > 0)  # 当前K线为阳线（收盘价高于开盘价）
            )

            # 布林带SMA做空条件：中轨和下轨都向下变化
            bb_sma_short_condition = (
                base_condition  # 基本条件
                & (df["bb_middle_sma_ratio"] < self.buy_bbl_sma_threshold.value)  # 中轨相对SMA下降
                & (df["bb_lower_sma_ratio"] < self.buy_bbl_sma_threshold.value)  # 下轨相对SMA下降
                & (df["price_change"] < 0)  # 当前K线为阴线（收盘价低于开盘价）
            )

            # 设置布林带SMA做多入场信号
            df.loc[
                (
                    bb_sma_long_condition  # SMA做多条件
                    & (df["bb_lower_sma_ratio"] < -0.0001)  # 下轨相对SMA明显下降（布林带扩张）
                ),
                ["enter_long", "enter_tag"],
            ] = (1, "bb_sma_long")

            # 设置布林带SMA做空入场信号
            df.loc[
                (
                    bb_sma_short_condition  # SMA做空条件
                    & (df["bb_upper_sma_ratio"] > 0.0001)  # 上轨相对SMA明显上升（布林带扩张）
                ),
                ["enter_short", "enter_tag"],
            ] = (1, "bb_sma_short")

        # =============================================================================
        # 布林带突破交易条件
        # =============================================================================
        if self.is_breakout_enable:
            # 布林带上轨突破做多条件
            # 逻辑：价格突破布林带上轨 + 布林带宽度正在扩张（市场波动增加）
            bb_upper_breakout_long_condition = (
                base_condition  # 基本条件（交易量过滤等）
                & df["bb_upper_breakout"]  # 价格突破布林带上轨且下轨向下变化
                & (df["bb_width_expansion_ratio"] > self.buy_bb_width_expansion_threshold.value)  # 布林带宽度扩张超过阈值
            )

            # 布林带下轨突破做空条件
            # 逻辑：价格突破布林带下轨 + 布林带宽度正在扩张（市场波动增加）
            bb_lower_breakout_short_condition = (
                base_condition  # 基本条件（交易量过滤等）
                & df["bb_lower_breakout"]  # 价格突破布林带下轨且上轨向上变化
                & (df["bb_width_expansion_ratio"] > self.buy_bb_width_expansion_threshold.value)  # 布林带宽度扩张超过阈值
            )

            # 设置布林带突破做多入场信号
            df.loc[
                bb_upper_breakout_long_condition,
                ["enter_long", "enter_tag"],
            ] = (1, "bb_upper_breakout_long")

            # 设置布林带突破做空入场信号
            df.loc[
                bb_lower_breakout_short_condition,
                ["enter_short", "enter_tag"],
            ] = (1, "bb_lower_breakout_short")

        # =============================================================================
        # ema指标

        return df

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        生成退出信号

        基于价格回归布林带中轨生成退出信号，当价格回到布林带中轨附近时退出仓位

        Args:
            df: 包含所有技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了退出信号的数据框
        """

        if self.is_fastk_enable:
            df.loc[qtpylib.crossed_above(df["fastk"], self.sell_long_fastx.value), ["exit_long", "exit_tag"]] = (1, "fastk_cross_exit_long")
            df.loc[qtpylib.crossed_below(df["fastk"], 100 - self.sell_short_fastx.value), ["exit_short", "exit_tag"]] = (1, "fastk_cross_exit_short")

        return df

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float, current_profit: float, **kwargs) -> str | None:
        """
        自定义退出逻辑

        提供额外的风险控制退出条件，包括反转信号退出和布林带中轨退出

        退出条件：
        1. 反转信号退出：当出现与当前仓位相反的入场信号时退出
           - 做多仓位：出现bb_sma_short信号时，根据盈亏状态决定退出
             * 盈利超过阈值：反转止盈退出
             * 亏损超过阈值：反转止损退出
           - 做空仓位：出现bb_sma_long信号时，根据盈亏状态决定退出
             * 盈利超过阈值：反转止盈退出
             * 亏损超过阈值：反转止损退出
        2. 布林带中轨退出：价格回归到布林带中轨时的盈亏保护
           - 做多：价格低于中轨时检查盈亏状态
           - 做空：价格高于中轨时检查盈亏状态

        Args:
            pair: 交易对名称
            trade: 当前交易对象，包含交易信息
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈利率（正数为盈利，负数为亏损）
            **kwargs: 其他参数

        Returns:
            str | None: 退出标签或None（不退出）
        """

        # =============================================================================
        # 计算持仓时间
        # =============================================================================
        # holding_time = current_time - trade.open_date_utc
        # holding_hours = holding_time.total_seconds() / 3600  # 转换为小时
        # # 时间止损：持仓超过4小时且处于亏损状态时退出
        # # 避免长时间持有亏损仓位，提高资金使用效率
        # if current_profit < 0:
        #     if holding_hours >= 4.0:
        #         return "time_loss_profit"

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)  # 获取分析后的数据
        if len(dataframe) < 1:
            return None
        df = dataframe.iloc[-1].squeeze()  # 获取最后一行数据

        # 做多仓位退出逻辑
        if not trade.is_short:
            # 检查反转信号：出现bb_sma_short信号时的退出逻辑
            if df.get("bb_sma_short", False):
                if current_profit > self.sell_reversal_profit_threshold.value:
                    return "reversal_profit_exit_long"  # 反转止盈退出
                elif current_profit < self.sell_reversal_loss_threshold.value:
                    return "reversal_loss_exit_long"  # 反转止损退出

            # 原有的布林带中轨退出逻辑
            if current_rate < df.get("bb_middleband", current_rate):
                if current_profit < self.sell_bbm_loss_profit_threshold.value:
                    return "bbm_loss_exit_long"
                if current_profit > self.sell_bbm_take_profit_threshold.value:
                    return "bbm_profit_exit_long"

            if df.get("bbl_ema200_dead_cross", False) and current_profit < df.get("bb_lowerband", current_rate):
                return "bbl_ema200_dead_cross_exit_long"
        # 做空仓位退出逻辑
        else:
            # 检查反转信号：出现bb_sma_long信号时的退出逻辑
            if df.get("bb_sma_long", False):
                if current_profit > self.sell_reversal_profit_threshold.value:
                    return "reversal_profit_exit_short"  # 反转止盈退出
                elif current_profit < self.sell_reversal_loss_threshold.value:
                    return "reversal_loss_exit_short"  # 反转止损退出

            # 原有的布林带中轨退出逻辑
            if current_rate > df.get("bb_middleband", current_rate):
                if current_profit < self.sell_bbm_loss_profit_threshold.value:
                    return "bbm_loss_exit_short"
                if current_profit > self.sell_bbm_take_profit_threshold.value:
                    return "bbm_profit_exit_short"

            if df.get("bbu_ema200_gold_cross", False) and current_profit > df.get("bb_upperband", current_rate):
                return "bbu_ema200_gold_cross_exit_short"
        return None

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        设置杠杆倍数

        动态设置交易杠杆，优先从config.json读取全局设置，
        如无配置则使用策略内部的超参数优化值

        Args:
            pair: 交易对名称
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: FreqTrade建议的杠杆倍数
            max_leverage: 交易所允许的最大杠杆倍数
            entry_tag: 入场标签
            side: 交易方向（long/short）
            **kwargs: 其他参数

        Returns:
            float: 实际使用的杠杆倍数（1.0到max_leverage之间）
        """

        # 优先从config.json读取全局杠杆设置
        if hasattr(self.config, "get") and "trading" in self.config:
            config_leverage = self.config.get("trading", {}).get("leverage", None)
            if config_leverage is not None:
                return min(max(float(config_leverage), 1.0), max_leverage)

        # 如果config.json中没有设置，使用策略内部超参数
        return min(max(float(self.buy_leverage.value), 1.0), max_leverage)
