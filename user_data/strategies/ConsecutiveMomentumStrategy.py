from datetime import datetime

from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IntParameter, IStrategy
from pandas import DataFrame


class ConsecutiveMomentumStrategy(IStrategy):
    """
    连续动量交易策略 (Consecutive Momentum Strategy)

    这是一个基于连续价格动量的双向交易策略，通过检测连续的价格上涨或下跌
    来判断市场动量方向，并在动量确认后进行相应的交易操作。

    策略核心逻辑：
    1. 连续上涨检测：当价格连续3次上涨时做多
    2. 连续下跌检测：当价格连续3次下跌时做空
    3. 反转退出：当趋势出现反转信号时退出仓位

    技术特点：
    - 使用3分钟时间框架，适合短期动量交易
    - 基于简单的价格比较，无需复杂技术指标
    - 支持双向交易（做多和做空）
    - 快速响应市场动量变化
    - 支持超参数优化调整连续次数阈值

    风险控制：
    - 固定止损保护
    - 基于反转信号的及时退出
    - 可调节的连续次数阈值

    适用场景：
    - 短期动量明显的市场
    - 波动性较高的交易对
    - 趋势性较强的时间段

    作者: FreqTrade用户
    时间框架: 3分钟
    """

    # 基础策略设置
    timeframe = "3m"  # 使用3分钟时间框架
    can_short = True  # 启用做空交易

    # ROI和止损设置
    minimal_roi = {"0": 0.05}  # 5%最小收益目标
    stoploss = -0.03  # 3%止损保护

    # 交易设置
    max_open_trades = 5  # 最大同时持仓数量

    # 性能优化设置
    process_only_new_candles = True  # 只处理新K线
    startup_candle_count = 10  # 启动需要的历史K线数量

    # 超参数配置 - 连续次数阈值
    buy_consecutive_count = IntParameter(2, 5, default=3, space="buy", optimize=True, load=True)

    # 超参数配置 - 反转确认阈值
    buy_reversal_threshold = DecimalParameter(0.001, 0.01, default=0.005, space="buy", optimize=True, load=True)

    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        计算策略所需的价格动量指标：
        1. 价格变化方向：比较当前收盘价与前一收盘价
        2. 连续上涨次数：使用滚动窗口统计连续上涨
        3. 连续下跌次数：使用滚动窗口统计连续下跌
        4. 价格变化率：计算相对价格变化幅度

        Args:
            df: OHLCV价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了技术指标的数据框
        """

        # 计算价格变化方向
        # 1表示上涨，0表示下跌或持平
        df["price_up"] = (df["close"] > df["close"].shift(1)).astype(int)
        df["price_down"] = (df["close"] < df["close"].shift(1)).astype(int)

        # 计算价格变化率
        df["price_change_pct"] = (df["close"] - df["close"].shift(1)) / df["close"].shift(1)

        # 计算连续上涨次数（滚动窗口内上涨次数）
        df["consecutive_up"] = df["price_up"].rolling(window=self.buy_consecutive_count.value).sum()

        # 计算连续下跌次数（滚动窗口内下跌次数）
        df["consecutive_down"] = df["price_down"].rolling(window=self.buy_consecutive_count.value).sum()

        # 计算最近的价格趋势强度
        df["trend_strength"] = df["price_change_pct"].rolling(window=self.buy_consecutive_count.value).sum()

        # 添加简单的波动率指标作为过滤条件
        df["volatility"] = df["price_change_pct"].rolling(window=5).std()

        # === 新增：记录连续上涨或下跌的总次数指标 ===

        # 计算当前连续上涨的实际次数（不使用滚动窗口）
        df["current_consecutive_up"] = self._calculate_current_consecutive(df["price_up"])

        # 计算当前连续下跌的实际次数（不使用滚动窗口）
        df["current_consecutive_down"] = self._calculate_current_consecutive(df["price_down"])

        # 计算历史最大连续上涨次数
        df["max_consecutive_up"] = df["current_consecutive_up"].expanding().max()

        # 计算历史最大连续下跌次数
        df["max_consecutive_down"] = df["current_consecutive_down"].expanding().max()

        # 计算连续趋势变化标志（从上涨转下跌或从下跌转上涨）
        df["trend_change"] = (
            (df["price_up"].shift(1) == 1) & (df["price_down"] == 1)  # 从上涨转下跌
            | (df["price_down"].shift(1) == 1) & (df["price_up"] == 1)  # 从下跌转上涨
        ).astype(int)

        return df

    def _calculate_current_consecutive(self, series):
        """
        计算当前连续出现1的次数

        这个方法计算序列中当前位置向前连续出现1的次数。
        例如：[0,1,1,1,0,1,1] 会返回 [0,1,2,3,0,1,2]

        Args:
            series: pandas Series，包含0和1的序列

        Returns:
            pandas Series: 每个位置当前连续1的次数
        """
        # 创建结果序列
        result = series.copy()
        result[:] = 0

        # 逐行计算连续次数
        consecutive_count = 0
        for i in range(len(series)):
            if series.iloc[i] == 1:
                consecutive_count += 1
                result.iloc[i] = consecutive_count
            else:
                consecutive_count = 0
                result.iloc[i] = 0

        return result

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场信号

        基于连续价格动量生成交易信号：

        做多条件：
        - 连续上涨次数达到阈值（默认3次）
        - 趋势强度为正（整体向上）
        - 波动率不过高（避免噪音交易）

        做空条件：
        - 连续下跌次数达到阈值（默认3次）
        - 趋势强度为负（整体向下）
        - 波动率不过高（避免噪音交易）

        Args:
            df: 包含技术指标的价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了入场信号的数据框
        """

        # 初始化入场信号列
        df["enter_long"] = 0
        df["enter_short"] = 0
        df["enter_tag"] = ""

        # 做多条件：当前连续上涨次数达到阈值
        long_condition = (
            (df["current_consecutive_up"] >= self.buy_consecutive_count.value)  # 当前连续上涨次数达到阈值
            & (df["trend_strength"] > 0)  # 整体趋势向上
            & (df["volatility"] < 0.02)  # 波动率不过高（小于2%）
        )

        # 做空条件：当前连续下跌次数达到阈值
        short_condition = (
            (df["current_consecutive_down"] >= self.buy_consecutive_count.value)  # 当前连续下跌次数达到阈值
            & (df["trend_strength"] < 0)  # 整体趋势向下
            & (df["volatility"] < 0.02)  # 波动率不过高（小于2%）
        )

        # 设置做多信号
        df.loc[long_condition, ["enter_long", "enter_tag"]] = (1, "consecutive_up")

        # 设置做空信号
        df.loc[short_condition, ["enter_short", "enter_tag"]] = (1, "consecutive_down")

        return df

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        填充退出信号

        基于趋势反转检测生成退出信号：

        多头退出条件：
        - 出现明显的下跌信号（价格开始下跌）
        - 价格变化率低于反转阈值

        空头退出条件：
        - 出现明显的上涨信号（价格开始上涨）
        - 价格变化率高于反转阈值

        Args:
            df: 包含技术指标的价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了退出信号的数据框
        """

        # 初始化退出信号列
        df["exit_long"] = 0
        df["exit_short"] = 0
        df["exit_tag"] = ""

        # 多头退出条件：价格开始下跌
        long_exit_condition = (
            (df["price_down"] == 1)  # 当前K线下跌
            & (df["price_change_pct"] < -self.buy_reversal_threshold.value)  # 下跌幅度超过阈值
        )

        # 空头退出条件：价格开始上涨
        short_exit_condition = (
            (df["price_up"] == 1)  # 当前K线上涨
            & (df["price_change_pct"] > self.buy_reversal_threshold.value)  # 上涨幅度超过阈值
        )

        # 设置多头退出信号
        df.loc[long_exit_condition, ["exit_long", "exit_tag"]] = (1, "reversal_down")

        # 设置空头退出信号
        df.loc[short_exit_condition, ["exit_short", "exit_tag"]] = (1, "reversal_up")

        return df

    def custom_exit(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        自定义退出逻辑

        提供更灵活的退出条件，包括：
        1. 时间止损：持仓时间过长时退出
        2. 盈利保护：达到一定盈利后设置保护性止损
        3. 动量衰减：当动量信号减弱时退出

        Args:
            pair: 交易对
            trade: 当前交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前收益率

        Returns:
            str or None: 退出标签，None表示不退出
        """

        # 获取最新的数据框和指标
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        # 计算持仓时间（分钟）
        trade_duration = int((current_time.timestamp() - trade.open_date_utc.timestamp()) // 60)

        # 时间止损：持仓超过30分钟（10个3分钟K线）
        if trade_duration > 30:
            return "time_exit"

        # 盈利保护：盈利超过2%后，如果回撤超过1%则退出
        if current_profit > 0.02:
            max_profit = max(0, trade.calc_profit_ratio(trade.max_rate))
            if current_profit < (max_profit - 0.01):
                return "profit_protection"

        # 动量衰减检测
        if not trade.is_short:  # 多头仓位
            # 如果当前连续上涨次数减少且趋势强度转负，退出多头
            if last_candle["current_consecutive_up"] < self.buy_consecutive_count.value and last_candle["trend_strength"] < 0:
                return "momentum_decay_long"
        else:  # 空头仓位
            # 如果当前连续下跌次数减少且趋势强度转正，退出空头
            if last_candle["current_consecutive_down"] < self.buy_consecutive_count.value and last_candle["trend_strength"] > 0:
                return "momentum_decay_short"

        # 极端亏损保护：亏损超过5%强制退出
        if current_profit < -0.05:
            return "emergency_exit"

        return None
