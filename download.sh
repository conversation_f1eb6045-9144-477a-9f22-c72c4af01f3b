#!/usr/bin/env bash
# shellcheck disable=SC1091

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

选项:
    -h, --help         显示此帮助信息
    -c, --config FILE  配置文件 (覆盖 CONFIG_FILE)
    -p, --pairs PAIRS  交易对列表 (空格分隔)
    --start-date DATE  开始日期 (覆盖 START_DATE)
    --prepend          允许数据前置 (数据追加被禁用)

示例:
    $0 -c user_data/config.json
    $0 -c user_data/config.json --prepend
    $0 -c user_data/config.json --start-date 20241231 --prepend

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 默认设置
USE_PAIRS_FILE=false
PAIRS_FILE="${DATA_DIR}/pairs.json"
PREPEND=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    -c | --config)
        CONFIG_FILE="$2"
        USE_PAIRS_FILE=false
        shift 2
        ;;
    -p | --pairs)
        PAIRS="$2"
        USE_PAIRS_FILE=false
        shift 2
        ;;
    --start-date)
        START_DATE="$2"
        shift 2
        ;;
    --now)
        END_NOW=true
        shift
        ;;
    --prepend)
        PREPEND=true
        shift
        ;;
    --use-pairs-file)
        USE_PAIRS_FILE=true
        shift
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

# 显示当前配置
echo "=== 下载配置 ==="
echo "交易所: ${EXCHANGE}"
echo "开始日期: ${START_DATE}"
echo "================"

if [[ "$END_NOW" == "true" ]]; then
    END_DATE=""
else
    END_DATE=$(date +"%Y%m%d")
fi

# 构建下载命令
freqtrade_cmd=(
    freqtrade download-data
    --data-dir "${DATA_DIR}"
    --exchange "${EXCHANGE}"
    --trading-mode futures
    --data-format-ohlcv feather
    --data-format-trades feather
    --timeframe 3m
    --timerange="${START_DATE}-${END_DATE}"
)

if [[ -n "$PAIRS" ]]; then
    echo "使用交易对: ${PAIRS}"
    freqtrade_cmd+=(--pairs "${PAIRS}")
elif [[ "$USE_PAIRS_FILE" == "true" && -f "${PAIRS_FILE}" ]]; then
    echo "使用交易对文件: ${PAIRS_FILE}"
    freqtrade_cmd+=(--pairs-file "${PAIRS_FILE}")
elif [[ -f "${CONFIG_FILE}" ]]; then
    echo "使用配置文件: ${CONFIG_FILE}"
    freqtrade_cmd+=(--config "${CONFIG_FILE}")
fi

if [ "$PREPEND" == "true" ]; then
    # 下载前面的数据
    freqtrade_cmd+=(--prepend)
fi

# 执行命令并检查是否成功
if "${freqtrade_cmd[@]}"; then
    echo "数据下载成功！"
    if [[ "$END_NOW" == "true" ]]; then
        # 计算30天前的日期
        THIRTY_DAYS_AGO=$(date -d "30 days ago" +"%Y%m%d" 2>/dev/null || date -v-30d +"%Y%m%d")
        # 更新.env文件中的START_DATE为30天前
        sed -i '' "s/START_DATE=\"[0-9]*\"/START_DATE=\"${THIRTY_DAYS_AGO}\"/" .env
        echo "已将START_DATE更新为30天前: ${THIRTY_DAYS_AGO}"

        # 修改.env END_DATE 为当前日期
        sed -i '' "s/END_DATE=\"[0-9]*\"/END_DATE=\"$(date +%Y%m%d)\"/" .env
        echo "已将END_DATE更新为当前日期: $(date +%Y%m%d)"
    fi
else
    echo "数据下载失败，未更新START_DATE"
    exit 1
fi
